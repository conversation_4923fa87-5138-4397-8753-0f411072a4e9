package main

import (
	"contentmanager/etc/conf"
	middlewares2 "contentmanager/infrastructure/middlewares"
	_ "contentmanager/library/helpers"
	_ "contentmanager/library/helpers/hbs_query"
	"contentmanager/library/httpService"
	"contentmanager/library/httpService/controller/status"
	publicControllers "contentmanager/library/tenant/public/controllers"
	_ "contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/matchredirect/middleware"
	"contentmanager/pkgs/multitenancy"
	_ "contentmanager/pkgs/plugins" // Import to register GORM plugins
	"contentmanager/pkgs/storage"
	"context"
	"flag"
	"fmt"
	_ "go.uber.org/automaxprocs"
	"net/http"
	"os"
	"os/signal"
	"time"
)

const serviceName = "cm-media"

func wireUpRouter(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	//Health Check
	r.Get(conf.RouterApiStatus, status.StatusController{}.CheckServerStatus)
	//Images
	//r.Get(conf.RouterImages, middlewares2.AllowAnonymousAccessMiddleware(), publicControllers.MediaController{}.GetTenantPublicMedia)
	//r.Get(conf.RouterThumbnails, middlewares2.AllowAnonymousAccessMiddleware(), publicControllers.MediaController{}.GetTenantPublicThumbnail)
	//Document
	r.Get(conf.RouterDocuments, publicControllers.DocumentController{}.RoutePublicDocument)
	// r.Get(conf.RouterDocumentsFilename, middlewares2.AllowAnonymousAccessMiddleware(), publicControllers.DocumentController{}.GetPublicDocument)
	return r
}

func main() {
	logging.ServiceName = serviceName
	log := logging.RootLogger()

	_ = os.Setenv("APP_NAME", serviceName)

	tenancyServiceConfig := config.Init()
	tenancyServiceConfig.ServicePort = "14000"

	var wait time.Duration
	flag.DurationVar(&wait, "graceful-timeout", time.Second*15, "the duration for which the server gracefully wait for existing connections to finish - e.g. 15s or 1m")
	flag.Parse()

	// Instantiate a new router
	m := middlewares2.Default()
	m = wireUpRouter(m)

	factory := multitenancy.AccessorFactoryCached(context.Background(), *tenancyServiceConfig)
	m.Map(factory)

	m.Use(middleware.MatchRedirectMiddleware(factory.Accessor(context.Background())))

	if tokenManager, err := token.NewTokenManager[identity.PublicAccount](token.Config{
		CookieName:       tenancyServiceConfig.CookieName,
		CookieKey:        tenancyServiceConfig.CookieKey,
		TokenExpiration:  conf.SessionCookieExpiry,
		OperationTimeout: time.Second * 5,
	}); err != nil {
		log.Fatal().Err(err).Msg("Error creating token manager")
	} else {
		m.Map(tokenManager)
	}

	m = storage.AddStorage(m)

	srv := &http.Server{
		Addr: tenancyServiceConfig.ServiceHost + ":" + tenancyServiceConfig.ServicePort,
		// Good practice to set timeouts to avoid Slowloris attacks.
		//WriteTimeout: Sending response to requester
		WriteTimeout: time.Second * 60,
		// ReadTimeout: Reading request from requester
		ReadTimeout: time.Second * 15,
		IdleTimeout: time.Second * 40,
		Handler:     m, // Pass our instance of gorilla/mux in.
	}

	log.Info().Msg(fmt.Sprintf("Starting up %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			log.Error().Stack().Err(err).
				Msg(fmt.Sprintf("Can't start %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
		}
	}()

	c := make(chan os.Signal, 1)
	// We'll accept graceful shutdowns when quit via SIGINT (Ctrl+C)
	// SIGKILL, SIGQUIT or SIGTERM (Ctrl+/) will not be caught.
	signal.Notify(c, os.Interrupt)

	// Block until we receive our signal.
	<-c

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), wait)
	defer cancel()
	// Doesn't block if no connections, but will otherwise wait
	// until the timeout deadline.
	srv.Shutdown(ctx)
	// Optionally, you could run srv.Shutdown in a goroutine and block on
	// <-ctx.Done() if your application should wait for other services
	// to finalize based on context cancellation.
	log.Info().Msg(fmt.Sprintf("Shutting down %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
	os.Exit(0)

}
