package main

import (
	"contentmanager/etc/conf"
	middlewares2 "contentmanager/infrastructure/middlewares"
	"contentmanager/library/binding"
	_ "contentmanager/library/helpers"
	_ "contentmanager/library/helpers/hbs_query"
	_ "contentmanager/library/helpers/images"
	"contentmanager/library/helpers/init_handlebars"
	_ "contentmanager/library/helpers/inline_partials"
	"contentmanager/library/httpService"
	"contentmanager/library/httpService/controller/status"
	"contentmanager/library/shared"
	"contentmanager/library/templates/hbs_helpers/keys"
	"contentmanager/library/templates/hbs_helpers/repository"
	hbsSharedHelpers "contentmanager/library/templates/hbs_helpers/shared"
	publicControllers "contentmanager/library/tenant/public/controllers"
	publicCalendar "contentmanager/library/tenant/public/features/calendar"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	login "contentmanager/pkgs/auth/login/routes"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/auth_links"
	"contentmanager/pkgs/config"
	public2 "contentmanager/pkgs/content/fragments/public"
	public4 "contentmanager/pkgs/content/public"
	"contentmanager/pkgs/forms/public"
	"contentmanager/pkgs/matchredirect/middleware"
	"contentmanager/pkgs/multitenancy"
	public5 "contentmanager/pkgs/notifications/public"
	public3 "contentmanager/pkgs/olm/public"
	_ "contentmanager/pkgs/plugins" // Import to register GORM plugins
	admin "contentmanager/pkgs/queries/public"
	"contentmanager/pkgs/sauth"
	public7 "contentmanager/pkgs/schoolfinder/public"
	"contentmanager/pkgs/search"
	public6 "contentmanager/pkgs/search_v2/public"
	"contentmanager/pkgs/settings"
	"contentmanager/pkgs/sitemap"
	"contentmanager/pkgs/storage"
	"contentmanager/pkgs/tags"
	"contentmanager/pkgs/third_party_content"
	"context"
	"flag"
	"fmt"
	_ "go.uber.org/automaxprocs"
	"net/http"
	// _ "net/http/pprof"
	"os"
	"os/signal"
	"time"
)

func init() {
	init_handlebars.InitHandlebars()
}

var serviceName = "Public Website Server"

func wireUpRouter(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	//Health Check
	r.Get(conf.RouterApiStatus, status.StatusController{}.CheckServerStatus)

	/*******************************************************************************************************************
	//Generic Public Controllers
	*******************************************************************************************************************/
	//Azure OAuth Authorize
	//r.Post(conf.RouterAuthorize, commonControllers.DirectoryLogin{}.Authorize)
	////////Google OAuth Authorize
	//r.Post(conf.RouterGoogleAuthorize, commonControllers.GoogleDirectoryLogin{}.Authorize)
	////////Authentication Routes
	//r.Post(conf.RouterLogin, commonControllers.AuthenticationController{}.Authenticate)
	//r.Get(conf.RouterLogout, commonControllers.AuthenticationController{}.Logout)

	r.Get(conf.RouterApiAlerts, publicControllers.ContentController{}.GetAlerts)
	////Event
	//r.Get(conf.RouterApiEvent, publicControllers.ContentController{}.GetCalendarEvents)
	r.Get(conf.RouterApiEvent, publicCalendar.PublicCalendarEvents_GET)
	////Media
	//r.Get(conf.RouterImages, publicControllers.MediaController{}.GetTenantPublicMedia)
	//r.Get(conf.RouterThumbnails, publicControllers.MediaController{}.GetTenantPublicThumbnail)
	////Document
	r.Get(conf.RouterDocuments, publicControllers.DocumentController{}.RoutePublicDocument)
	// r.Get(conf.RouterDocumentsFilename, publicControllers.DocumentController{}.GetPublicDocument)
	r.Get(conf.RouterFolderByFilename, publicControllers.DocumentController{}.GetPublicFolder)
	r.Get(conf.RouterFolderByFilename+"/:filename", publicControllers.DocumentController{}.GetPublicFolder)
	r.Get(conf.RouterRootDocuments, publicControllers.DocumentController{}.GetPublicFolder)
	////District - Bus Routes
	r.Get(conf.RouterApiBusRoute, publicControllers.TransportationController{}.GetPublicBusRoute)
	r.Get(conf.RouterApiBusArea, publicControllers.TransportationController{}.GetPublicBusArea)
	r.Get(conf.RouterApiBusRouteWithStatus, publicControllers.TransportationController{}.GetCombinedBusStatus)
	////Site Specific - Bus Routes
	r.Get(conf.RouterApiBusRouteBySite, publicControllers.TransportationController{}.GetPublicBusRouteBySite)
	r.Get(conf.RouterApiBusAreaBySite, publicControllers.TransportationController{}.GetPublicBusAreasBySite)
	r.Get(conf.RouterApiBusRouteWithStatusBySite, publicControllers.TransportationController{}.GetCombinedBusStatusBySite)
	///*******************************************************************************************************************
	////Generic API Controllers
	//*******************************************************************************************************************/
	//// Public Contact Mail Relay
	r.Post(conf.RouterContact, publicControllers.ContactController{}.PublicContactPost)

	//// Public Calendar feed
	r.Get(conf.RouterCalendarFeed, publicControllers.ContentController{}.GetCalendarFeed)
	// TODO => Deprecation warning
	//  This route is deprecated and has been left for backwards-compatibility
	//  Templates using this route (Calendar / Event Subscription) should be updated
	r.Get(conf.RouterAccountCalendarFeed, publicControllers.ContentController{}.GetCalendarFeed)
	//// Public News
	r.Get(conf.RouterApiNews, publicControllers.ContentController{}.GetNews)

	// HBS query
	r.Get("/sys/api/query", func(w http.ResponseWriter, r *shared.AppContext) {
		var q hbsSharedHelpers.HandlebarsQueryParams
		if err := binding.MapFromMaps(&q, r.Maps()); err != nil {
			utils.WriteResponseJSON(w, nil, err)
			return
		}

		keys := keys.NewKeys()
		repo := repository.NewHbsRepository(r, keys)
		res := repo.Query(q)
		if res.IsError() {
			utils.WriteResponseJSON(w, nil, res.Unwrap())
			return
		}
		result := map[string]interface{}{
			"Rows":  res.Data.Rows,
			"Total": res.Data.TotalPages,
			"Page":  res.Data.Page,
		}
		utils.WriteResponseJSON(w, result, nil)
	})

	r.Get("/sys/settings/api/:name", func(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
		name := p["name"]
		query := r.Request().URL.Query().Get("query")

		res := settings.FirstPublicByName(r, name, query)
		utils.WriteResultJSON(w, res)
	})

	r.NotFound(publicControllers.ContentController{}.GetContentPage)
	return r
}

func main() {
	logging.ServiceName = "cm-public"
	log := logging.RootLogger()
	_ = os.Setenv("APP_NAME", "cm-public")

	tenancyServiceConfig := config.Init()
	tenancyServiceConfig.ServicePort = "12000"

	var wait time.Duration
	flag.DurationVar(&wait, "graceful-timeout", time.Second*15, "the duration for which the server gracefully wait for existing connections to finish - e.g. 15s or 1m")
	flag.Parse()

	// Instantiate a new router
	m := middlewares2.Default()

	m = wireUpRouter(m)

	factory := multitenancy.AccessorFactoryCached(context.Background(), *tenancyServiceConfig)
	m.Map(factory)

	m.Use(middleware.MatchRedirectMiddleware(factory.Accessor(context.Background())))

	if tokenManager, err := token.NewTokenManager[identity.PublicAccount](token.Config{
		CookieName:       tenancyServiceConfig.CookieName,
		CookieKey:        tenancyServiceConfig.CookieKey,
		TokenExpiration:  conf.SessionCookieExpiry,
		OperationTimeout: time.Second * 5,
	}); err != nil {
		log.Fatal().Err(err).Msg("Error creating token manager")
	} else {
		m.Map(tokenManager)
	}

	m = sauth.AddSAuth(m, sauth.MapSocAuthConfig(*tenancyServiceConfig))
	m = public5.AddNotifications(m)
	m = public2.AddPublicFragments(m)
	m = public3.AddPublicOLM(m)
	m = public4.AddPublicContentV2(m)
	m = tags.AddTags(m)
	m = search.AddPublicRoutes(m)
	m = public.AddPublicForms(m)
	m = sitemap.AddPublicRoute(m)
	m = auth_links.AddAuthLinks(m)
	m = public6.AddPublicSearch(m)
	m = storage.AddStorage(m)
	m = third_party_content.AddPublic(m)
	m = login.AddRoutes(m)
	m = public7.AddPublicGeoSearch(m)
	m = admin.AddQueriesPublic(m)

	// *Note - Context from Commit: c00ecda64ccf69e11fb879a608702903f29a9ba5*
	// If this needs to be re-implemented, need to look at mechanism for establishing session
	// Currently based on Admin API use, which disallows establishing session for anonymous users,
	// accounts that: !isAdmin && len(securityGroups) == 0
	// m.Use(httpService.DatabaseMiddleware())

	srv := &http.Server{
		Addr: tenancyServiceConfig.ServiceHost + ":" + tenancyServiceConfig.ServicePort,
		// Good practice to set timeouts to avoid Slowloris attacks.
		WriteTimeout: time.Second * 15,
		ReadTimeout:  time.Second * 15,
		IdleTimeout:  time.Second * 60,
		Handler:      m, // Pass our instance of gorilla/mux in.
	}

	log.Info().Msg(fmt.Sprintf("Starting up %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			log.Error().Stack().Err(err).
				Msg(fmt.Sprintf("Can't start %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
		}
	}()

	c := make(chan os.Signal, 1)
	// We'll accept graceful shutdowns when quit via SIGINT (Ctrl+C)
	// SIGKILL, SIGQUIT or SIGTERM (Ctrl+/) will not be caught.
	signal.Notify(c, os.Interrupt)

	// Block until we receive our signal.
	<-c

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), wait)
	defer cancel()
	// Doesn't block if no connections, but will otherwise wait
	// until the timeout deadline.
	srv.Shutdown(ctx)
	// Optionally, you could run srv.Shutdown in a goroutine and block on
	// <-ctx.Done() if your application should wait for other services
	// to finalize based on context cancellation.
	log.Info().Msg(fmt.Sprintf("Shutting down %s, host %s on Port: %s", serviceName, tenancyServiceConfig.ServiceHost, tenancyServiceConfig.ServicePort))
	os.Exit(0)

}
