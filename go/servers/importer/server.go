package main

import (
	"contentmanager/pkgs/service_context"
	_ "contentmanager/pkgs/plugins" // Import to register GORM plugins
	importers "contentmanager/servers/importer/common"
	"contentmanager/servers/importer/common/integration"
	"contentmanager/servers/importer/edsby"
	"contentmanager/servers/importer/facebook"
	"contentmanager/servers/importer/google_calendar"
	"contentmanager/servers/importer/outlook"
	_ "go.uber.org/automaxprocs"
	"time"
)

func main() {
	sc := service_context.NewServiceContext("importer")
	var timeBetweenImports = sc.TenancyConfig().GetImportSchedule()

	for {
		tenants := sc.Tenants()
		l := sc.Logger()

		// Process per-tenant - Import(db, tenant)
		// Ideally we get the DB connection here, which means we don't have to do it per-import.
		// Ideally we don't have to also pass the tenant down, however, for any imports needing Media, it is required (Uploader requires TenantID)
		for _, tenant := range tenants {
			db := sc.TenantDB(tenant.ID)
			if db == nil {
				l.Error().Str("tenant", tenant.Name).Msg("nil database connection")
				return
			}
			importer := integration.NewImportContext(db, tenant)

			facebook.Import(db, tenant)
			google_calendar.Import(importer)
			edsby.Import(importer)
			outlook.Import(importer)

			if err := importers.ClearImportHistory(db); err != nil {
				importer.Logger.Err(err).
					Str("tenant", tenant.Name).
					Msg("failed to clear history for imported content")
			}
		}
		t := time.Now().UTC().Add(timeBetweenImports)
		l.Info().
			Str("ScheduledUTC", t.Format(time.DateTime)).
			Str("ScheduledPST", toPSTString(t)).
			Msg("Completed Import, awaiting next scheduled run-time")
		time.Sleep(timeBetweenImports)
	}
}
func toPSTString(time2 time.Time) string {
	if loc, err := time.LoadLocation("America/Vancouver"); err == nil {
		return time2.In(loc).Format(time.DateTime)
	}
	return ""
}
