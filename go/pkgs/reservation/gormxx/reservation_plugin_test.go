package gormxx

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils/converters"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/content"
	"contentmanager/tests"
	"context"
	"encoding/json"
	"github.com/araddon/dateparse"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
	"testing"
	"time"
)

var (
	userID  = uuid.FromStringOrNil("********-0000-0000-0000-************")
	account = identity.Account{
		ID: userID,
	}
	now    = time.Now()
	future = now.Add(time.Hour)
	past   = now.Add(-time.Hour)
)

func Test_ReservationPlugin(t *testing.T) {
	ctx := context.Background() //tests.InitLogging("Test_Promotions")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	if err := db.Create(&account).Error; err != nil {
		t.Fatal(err)
	}

	if err := db.Use(ReservationPlugin{}); err != nil {
		t.Fatal(err)
	}

	appContext := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
		TenantID:  uuid.UUID{},
		SiteID:    uuid.UUID{},
		TenantDB:  db.WithContext(ctx),
		TenancyDB: nil,
		Identity: &identity.Account{
			ID: userID,
		},
		Request: nil,
	})

	c := getContent()
	if err := appContext.TenantDatabase().Save(&c).Error; err != nil {
		t.Fatal(err)
	}

	// 	t.Log(time.Now().UTC().Format(time.RFC3339))
}

func getContent() content.Content {
	return content.Content{
		ID:          uuid.NewV4(),
		Workspace:   "live",
		EffectiveIn: []string{"draft"},
		Base: content.Base{
			SharableBase: auth.SharableBase{
				Sites:        []uuid.UUID{uuid.UUID{}},
				DepartmentID: nil,
			},
			PrivacyLevel: 0,
			PublishPeriod: content.PublishPeriod{
				PublishAt: converters.AsPointer(time.Now()),
				ExpireAt:  nil,
			},
		},
		Active:      true,
		Type:        "page",
		Owner:       userID,
		Publisher:   userID,
		Title:       "Test",
		Content:     "Test",
		Data:        nil,
		Structure:   nil,
		Route:       "/test",
		Path:        "path",
		PageLayout:  "HTML",
		Created:     time.Now(),
		Updated:     time.Now(),
		Deleted:     nil,
		Approved:    false,
		MediaID:     nil,
		Settings:    json.RawMessage("{}"),
		StructureID: nil,
		Meta:        datatypes.JSONType[map[string]string]{},
		Structures:  nil,
		Tags:        []uuid.UUID{},
	}
}

func Test_DatesComparison(t *testing.T) {
	tUTC := time.Now().UTC()
	tLocal := tUTC.Local()

	t.Log(tUTC.Equal(tLocal))
}

func Test_ParseJSDate(t *testing.T) {
	// toISOString()
	tm, err := dateparse.ParseAny("2025-06-11T17:03:36.902Z")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tm)
	t.Log(tm.Local())
}
