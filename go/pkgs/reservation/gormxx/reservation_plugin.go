package gormxx

import (
	appContext "contentmanager/library/context"
	"contentmanager/pkgs/reservation/models"
	"errors"
	"github.com/araddon/dateparse"
	"gorm.io/gorm"
	"reflect"
)

type ReservationPlugin struct{}

func (p ReservationPlugin) Name() string {
	return "reservationPlugin"
}

func (p ReservationPlugin) Initialize(db *gorm.DB) error {
	if err := db.Callback().Update().Before("gorm:update").Register("reservation:before_update", p.beforeUpdate); err != nil {
		return err
	}

	return nil
}

func (p ReservationPlugin) beforeUpdate(db *gorm.DB) {
	p.checkReservations(db)
}

func (p ReservationPlugin) checkReservations(db *gorm.DB) {
	if db.Statement.Schema == nil {
		return
	}

	switch db.Statement.ReflectValue.Kind() {
	case reflect.Struct:
		// Example: Check if this is a reservation-related model
		if reservable, ok := db.Statement.ReflectValue.Interface().(interface{ ReservationKey() string }); ok {
			validateReservations(db, []string{reservable.ReservationKey()})
		}
	case reflect.Slice, reflect.Array:
		var keys []string
		for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
			item := db.Statement.ReflectValue.Index(i)
			if reservable, ok := item.Interface().(interface{ ReservationKey() string }); ok {
				keys = append(keys, reservable.ReservationKey())
			}
		}
		validateReservations(db, keys)
	}
}

func validateReservations(db *gorm.DB, keys []string) {
	if len(keys) == 0 {
		return
	}

	r, ok := db.Statement.Context.Value("app_context").(appContext.AppContextProvider)
	if !ok {
		db.AddError(errors.New("[validateReservations] app_context not found in context"))
		return
	}

	if r.Request() == nil || len(r.Request().Form.Get("ie_editing_session")) == 0 {
		// TODO: @Anatoly should we fail or ignore? If ignore, should we proceed and fail if someone else has a lock?
		db.AddError(errors.New("[validateReservations] request not found in context"))
		return
	}

	if r.Account() == nil {
		db.AddError(errors.New("[validateReservations] account not found in context"))
		return
	}

	editingSession, err := dateparse.ParseAny(r.Request().Form.Get("ie_editing_session"))
	if err != nil {
		db.AddError(err)
		return
	}

	userID := r.Account().ID

	var reservations []models.Reservation
	if err := db.Where("key in ?", keys).
		Find(&reservations).Error; err != nil {
		db.AddError(err)
		return
	}

	var errs []error
	for _, rsrv := range reservations {
		if e := rsrv.AvailableFor(userID, editingSession); e != nil {
			errs = append(errs, e)
		}
	}

	if len(errs) > 0 {
		db.AddError(errors.Join(errs...))
	}
}

//// Register the plugin
//db.Use(ReservationPlugin{})
