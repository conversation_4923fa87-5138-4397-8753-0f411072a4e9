package errxx

import (
	"errors"
	"fmt"
)

// BaseError provides structured error handling with context data and cause chaining
type BaseError struct {
	msg   string
	data  any
	cause error
}

// New creates a new BaseError with message and optional context data
func New(msg string, data ...any) *BaseError {
	var d any
	if len(data) > 0 {
		d = data[0]
	}
	return &BaseError{msg: msg, data: d}
}

// Wrap wraps an existing error with additional context
func Wrap(cause error, msg string, data ...any) *BaseError {
	if cause == nil {
		return New(msg, data...)
	}

	var d any
	if len(data) > 0 {
		d = data[0]
	}
	return &BaseError{msg: msg, data: d, cause: cause}
}

// Wrapf wraps an error with a formatted message
func Wrapf(cause error, format string, args ...any) *BaseError {
	return Wrap(cause, fmt.Sprintf(format, args...))
}

// Error implements the error interface
func (e *BaseError) Error() string {
	if e.cause != nil {
		return fmt.Sprintf("%s: %v", e.msg, e.cause)
	}
	return e.msg
}

// Unwrap implements the unwrapping interface for Go 1.13+ error handling
func (e *BaseError) Unwrap() error {
	return e.cause
}

// Data returns the context data associated with this error
func (e *BaseError) Data() any {
	return e.data
}

// Message returns just the error message without the cause chain
func (e *BaseError) Message() string {
	return e.msg
}

// Join creates an error that wraps multiple errors
func Join(msg string, errs ...error) *BaseError {
	if len(errs) == 0 {
		return New(msg)
	}

	// Filter out nil errors
	filtered := make([]error, 0, len(errs))
	for _, err := range errs {
		if err != nil {
			filtered = append(filtered, err)
		}
	}

	if len(filtered) == 0 {
		return New(msg)
	}

	joined := errors.Join(filtered...)
	return &BaseError{msg: msg, cause: joined}
}

// NotFoundError represents a resource not found error
type NotFoundError struct {
	*BaseError
	Resource string
}

// NewNotFoundError creates a new NotFoundError
func NewNotFoundError(resource string, data ...any) *NotFoundError {
	msg := fmt.Sprintf("%s not found", resource)
	var d any
	if len(data) > 0 {
		d = data[0]
	}

	return &NotFoundError{
		BaseError: &BaseError{msg: msg, data: d},
		Resource:  resource,
	}
}

// NotFound creates a NotFoundError with optional formatted message
func NotFound(resource string, data ...any) *NotFoundError {
	return NewNotFoundError(resource, data...)
}

// PermissionError represents a permission denied error
type PermissionError struct {
	*BaseError
	Operation string
}

// NewPermissionError creates a new PermissionError
func NewPermissionError(operation string, data ...any) *PermissionError {
	msg := fmt.Sprintf("permission denied for %s", operation)
	var d any
	if len(data) > 0 {
		d = data[0]
	}

	return &PermissionError{
		BaseError: &BaseError{msg: msg, data: d},
		Operation: operation,
	}
}

// PermissionDenied creates a PermissionError with optional formatted message
func PermissionDenied(operation string, data ...any) *PermissionError {
	return NewPermissionError(operation, data...)
}

// ValidationError represents a validation error
type ValidationError struct {
	*BaseError
	Field string
	Value any
}

// NewValidationError creates a new ValidationError
func NewValidationError(field string, value any, msg string, data ...any) *ValidationError {
	fullMsg := fmt.Sprintf("validation failed for field '%s': %s", field, msg)
	var d any
	if len(data) > 0 {
		d = data[0]
	}

	return &ValidationError{
		BaseError: &BaseError{msg: fullMsg, data: d},
		Field:     field,
		Value:     value,
	}
}

// InvalidInput creates a ValidationError for invalid input
func InvalidInput(field string, value any, reason string, data ...any) *ValidationError {
	return NewValidationError(field, value, reason, data...)
}

// Sentinel errors for use with errors.Is()
var (
	ErrNotFound         = &NotFoundError{}
	ErrPermissionDenied = &PermissionError{}
	ErrValidation       = &ValidationError{}
)

func (e *NotFoundError) Is(target error) bool {
	_, ok := target.(*NotFoundError)
	return ok
}

func (e *PermissionError) Is(target error) bool {
	_, ok := target.(*PermissionError)
	return ok
}

func (e *ValidationError) Is(target error) bool {
	_, ok := target.(*ValidationError)
	return ok
}

// GetData extracts data from a BaseError if possible
func GetData(err error) any {
	var be *BaseError
	if errors.As(err, &be) {
		return be.Data()
	}
	return nil
}
