package multitenancy

import (
	"gorm.io/gorm"
)

// PluginRegistry holds a list of GORM plugins to be registered with database connections
type PluginRegistry struct {
	plugins []gorm.Plugin
}

// GlobalPluginRegistry is the global instance of the plugin registry
var GlobalPluginRegistry = &PluginRegistry{}

// RegisterPlugin adds a plugin to the global registry
func RegisterPlugin(plugin gorm.Plugin) {
	GlobalPluginRegistry.plugins = append(GlobalPluginRegistry.plugins, plugin)
}

// ApplyPlugins applies all registered plugins to a GORM database instance
func (pr *PluginRegistry) ApplyPlugins(db *gorm.DB) error {
	for _, plugin := range pr.plugins {
		if err := db.Use(plugin); err != nil {
			return err
		}
	}
	return nil
}

// ApplyPlugins applies all globally registered plugins to a GORM database instance
func ApplyPlugins(db *gorm.DB) error {
	return GlobalPluginRegistry.ApplyPlugins(db)
}
