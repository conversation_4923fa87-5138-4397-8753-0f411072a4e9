package utils

import (
	"contentmanager/library/shared/result"
	"contentmanager/logging"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/reservation"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
	"strconv"
)

type (
	ResponseObject struct {
		Results   interface{}       `json:"results"`
		ResultSet ResponseResultSet `json:"resultset"`
	}
	ResponseResultSet struct {
		TotalRecords int `json:"total_records"`
		Offset       int `json:"offset"`
		Limit        int `json:"limit"`
	}
	UnauthorizedError struct {
		Id      uuid.UUID
		Message string
	}
)

func (e UnauthorizedError) Error() string {
	if len(e.Message) > 0 {
		return e.Message
	}
	return fmt.Sprintf("You can't edit Content with ID: %s", e.Id)
}

func (ro ResponseObject) ToMap() map[string]interface{} {
	var results map[string]interface{}
	b, _ := json.Marshal(ro)
	json.Unmarshal(b, &results)
	return results
}

func EncodeJSON(w http.ResponseWriter, req *http.Request, i interface{}) {
	err := json.NewEncoder(w).Encode(i)
	if err != nil {
		logging.FromContext(req.Context()).Error().Err(err).Msg("error encoding response")
		ResponseJson(w, Message("error encoding response: \n "+err.Error()), http.StatusExpectationFailed)
	}
}
func NewResponse(offset, limit string) ResponseObject {
	li, _ := strconv.Atoi(limit)
	os, _ := strconv.Atoi(offset)
	return ResponseObject{
		Results: []interface{}{},
		ResultSet: ResponseResultSet{
			TotalRecords: 0,
			Offset:       os,
			Limit:        li,
		},
	}
}

func Message(message string) map[string]interface{} {
	return map[string]interface{}{"message": message}
}

func ResponseJson(w http.ResponseWriter, data map[string]interface{}, statusCode int) {
	/*
		{
			results{
				object []
			},
			resultset{
				total_records:
				offset:
				limit:
			}
		}
	*/

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

func WriteResultJSON(w http.ResponseWriter, result result.IResult) {
	WriteResponseJSON(w, result.GetData(), result.Unwrap())
}

func WriteResponseJSON(w http.ResponseWriter, data interface{}, err error) {
	if err == nil {
		if data == nil {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(data)

	} else {
		WriteError(w, err)
	}
}

var ErrNotFound = errors.New("not found")

func WriteError(w http.ResponseWriter, err error) {
	status := http.StatusBadRequest
	data := map[string]interface{}{
		"Success":      false,
		"ErrorMessage": err.Error(),
	}

	if x, ok := err.(interface{ Data() any }); ok {
		data["Data"] = x.Data()
	}

	switch {
	case errors.As(err, &UnauthorizedError{}), errors.Is(err, auth.ForbiddenError):
		status = http.StatusForbidden
	case errors.Is(err, reservation.ErrEditingSessionConflict):
		status = http.StatusConflict
	default:
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, ErrNotFound) {
			status = http.StatusNotFound
		} else {
			status = http.StatusBadRequest
		}
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func WriteStatusJSON(w http.ResponseWriter, status int, data any) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_ = json.NewEncoder(w).Encode(data)
}

func WriteHTML(w http.ResponseWriter, html string, status int) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.WriteHeader(status)
	_, _ = w.Write([]byte(html))
}
