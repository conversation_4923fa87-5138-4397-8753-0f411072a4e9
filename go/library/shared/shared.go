package shared

import (
	"contentmanager/infrastructure/database/pgxx"
	appContext "contentmanager/library/context"
	tenancyModels "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/mapxx"
	"contentmanager/library/utils/slicexx"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/identity/accessor"
	"contentmanager/pkgs/multitenancy"
	"context"
	"encoding/json"
	"errors"
	"github.com/araddon/dateparse"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
	"net/url"
	"reflect"
	"sort"
	"strings"
	"time"
)

type AppContext struct {
	tenantID        uuid.UUID
	tenant          *multitenancy.Tenant
	currentSiteID   *uuid.UUID
	currentSite     *multitenancy.Site
	mta             multitenancy.Accessor
	accountAccessor accessor.Accessor
	request         *http.Request
	sites           []tenancyModels.Site
	sitesMap        map[uuid.UUID]tenancyModels.Site
	tenantDB        *gorm.DB
	currentSiteVM   *tenancyModels.SiteViewModel
	tags            []commonModels.Tag
	folders         map[string]string
	workspaces      []string
}

// Ensure AppContext implements AppContextProvider interface
var _ appContext.AppContextProvider = (*AppContext)(nil)

func NewAppContext(r *http.Request, mta multitenancy.Accessor, acc accessor.Accessor, tenantID uuid.UUID) *AppContext {
	appCtx := &AppContext{
		mta:             mta,
		accountAccessor: acc,
		request:         r,
		tenantID:        tenantID,
	}
	tenantDB := mta.TenantDB(tenantID).WithContext(context.WithValue(r.Context(), "app_context", appCtx))
	appCtx.tenantDB = tenantDB

	if !strings.Contains(r.Host, ".contentmanager.imagineeverything.") {
		if d, err := mta.TenantSiteIDsByHost(r.Host); err == nil {
			appCtx.currentSiteID = &d.SiteID
		}
	}

	return appCtx
}

var AppTime IAppTime = &systemTime{}

func (r *AppContext) Logger() *zerolog.Logger {
	log := logging.FromContext(r.Request().Context())
	if log == zerolog.DefaultContextLogger {
		l := logging.RootLogger()
		log = l
	}
	return log
}

func (r *AppContext) IsAdminSite() bool {
	return strings.Contains(r.Request().Host, ".contentmanager.imagineeverything.")
}

func (r *AppContext) CurrentSiteDomain() string {
	return r.Request().Host
}

func (r *AppContext) URL() *url.URL {
	if r.Request().URL.IsAbs() {
		return r.Request().URL
	}

	return &url.URL{
		Scheme: "https",
		Host:   r.Request().Host,
		Path:   r.Request().URL.Path,
	}
}

func (r *AppContext) AccountWithGroups() error {
	if !r.accountAccessor.Authenticated() {
		return errors.New("Account not authenticated. ")
	}

	_ = r.accountAccessor.Account()
	return nil
}

func (r *AppContext) AppTime() IAppTime {
	return AppTime
}

func (r *AppContext) ContextValue(key string) string {
	ctx := r.Request().Context()
	for ctx != nil {
		if val := ctx.Value(key); val != nil {
			if str, ok := val.(string); ok {
				return str
			}
		}
		ctx = getParentContext(ctx)
	}
	return ""
}

func getParentContext(ctx context.Context) context.Context {
	val := reflect.ValueOf(ctx)
	if val.Kind() != reflect.Struct {
		return nil
	}
	parentField := val.FieldByName("Context") // 'Context' is the field name in WithCancel, WithDeadline, WithTimeout types
	if !parentField.IsValid() {
		return nil
	}
	if parent := parentField.Interface(); parent != nil {
		if pCtx, ok := parent.(context.Context); ok {
			return pCtx
		}
	}
	return nil
}

func (r *AppContext) Maps() map[string][]string {
	form := map[string][]string{}
	mapxx.Copy(form, r.Request().URL.Query())
	mapxx.Copy(form, r.Request().Form)
	mapxx.Copy(form, r.Request().PostForm)
	if r.Request().MultipartForm != nil {
		mapxx.Copy(form, r.Request().MultipartForm.Value)
	}

	if pp := r.Request().Context().Value("params"); pp != nil {
		reflectedVal := reflect.ValueOf(pp)
		if reflectedVal.Kind() == reflect.Map {
			iter := reflectedVal.MapRange()
			for iter.Next() {
				k := iter.Key().String()
				v := iter.Value().String()
				form[k] = []string{v}
			}
		}
	}

	return form
}

func (r *AppContext) Sites() ([]tenancyModels.Site, error) {
	if r.sites != nil {
		return r.sites, nil
	}
	ss, err := r.mta.TenantSites(r.tenantID)
	if err != nil {
		return nil, err
	}
	sites := make([]tenancyModels.Site, 0, len(ss))
	for _, s := range ss {
		sites = append(sites, tenancyModels.Site{
			BaseSite: tenancyModels.BaseSite{
				ID:            s.ID,
				TenantID:      s.TenantID,
				Name:          s.Name,
				PrimaryDomain: s.PrimaryDomain,
				Description:   s.Description,
				Type:          tenancyModels.SiteType(s.Type),
				Active:        s.Active,
			},
			Created:     s.Created,
			Settings:    s.Settings,
			Tags:        s.Tags,
			Hosts:       nil,
			Departments: nil,
		})
	}

	//if err := r.TenancyDB().Where(" tenant_id = ?", r.TenantID()).
	//	Where(" active = true ").
	//	Preload("Hosts", "active = true").
	//	//Preload("Departments", "active = true").
	//	Find(&sites).
	//	Error; err != nil {
	//	return nil, err
	//}
	r.sites = sites
	return sites, nil
}

func (r *AppContext) SiteStructs(ids []uuid.UUID) []tenancyModels.Site {
	res := []tenancyModels.Site{}

	for _, id := range ids {
		if site, err := r.SiteByID(id); err == nil {
			site.Settings = nil
			res = append(res, site)
		} else {
			r.Logger().Error().Err(err).Msgf("[SiteStructs] Site not found in tenant: site: %s, tenant: %s", id, r.tenantID)
		}
	}

	return res
}

func (r *AppContext) BestSite(ids []uuid.UUID) tenancyModels.Site {
	currentSiteID := r.CurrentSiteID()
	for _, id := range ids {
		if id == currentSiteID {
			if site, err := r.SiteByID(id); err == nil {
				return site
			} else {
				r.Logger().Error().Err(err).Msgf("[BestSite] Current Site not found in tenant: site: %s, tenant: %s", id, r.tenantID)
				break
			}
		}
	}

	for _, id := range ids {
		if site, err := r.SiteByID(id); err == nil {
			return site
		}
	}

	r.Logger().Error().Msgf("[BestSite] No sites from ids found in tenant: sites: %v, tenant: %s", ids, r.tenantID)
	return tenancyModels.Site{}
}

func (r *AppContext) SiteByID(id uuid.UUID) (tenancyModels.Site, error) {
	if r.sitesMap == nil {
		r.sitesMap = map[uuid.UUID]tenancyModels.Site{}
		sites, err := r.Sites()
		if err != nil {
			return tenancyModels.Site{}, err
		}
		for _, site := range sites {
			r.sitesMap[site.ID] = site
		}
	}
	if site, ok := r.sitesMap[id]; ok {
		return site, nil
	}

	return tenancyModels.Site{}, errors.New("Site not found. ")
}

func (r *AppContext) SetSites(sites []tenancyModels.Site) {
	r.sites = sites
}

func (r *AppContext) TenantDatabase() *gorm.DB {
	return r.tenantDB
}

func (r *AppContext) TagIDsToNames(ids []uuid.UUID) []string {
	var names []string

	if err := r.loadTags(); err != nil {
		return []string{}
	}

	if len(ids) == 0 {
		return names
	}

	if r.tags == nil {
		if err := r.TenantDatabase().Where(pgxx.FieldInArray("id", ids)).Find(&r.tags).Error; err != nil {
			r.Logger().Error().Err(err).Msgf("[AppContext.TagIDsToNames] Failed to get tags for IDs: %v", ids)
			return names
		}
	}

	mappedTags := map[uuid.UUID]string{}
	for _, tag := range r.tags {
		mappedTags[tag.ID] = tag.Name
	}

	for _, id := range ids {
		if name, ok := mappedTags[id]; ok {
			names = append(names, name)
		} else {
			r.Logger().Error().Msgf("[AppContext.TagIDsToNames] Tag not found: %s", id)
		}
	}

	sort.Strings(names)
	return names
}

func (r *AppContext) TagNamesToIDs(tagsType string, names []string) []uuid.UUID {
	var ids []uuid.UUID

	if err := r.loadTags(); err != nil {
		return ids
	}

	var tagsTypes []string
	switch tagsType {
	case "content":
		tagsTypes = []string{"page", "news", "event", "alert", "fragment"}
	case "media":
		tagsTypes = []string{"image", "document"}
	default:
		tagsTypes = []string{tagsType}
	}
	if len(names) == 0 {
		return ids
	}

	mappedTags := map[string]uuid.UUID{}
	for _, tag := range r.tags {
		if !slicexx.HasCommonElement(tagsTypes, tag.Types) {
			continue
		}
		mappedTags[strings.ToLower(tag.Name)] = tag.ID
	}

	for _, name := range names {
		// either a tag id or a tag name
		if id, err := uuid.FromString(name); err == nil {
			ids = append(ids, id)
			continue
		}

		if id, ok := mappedTags[strings.ToLower(name)]; ok {
			ids = append(ids, id)
			continue
		}
		r.Logger().Error().Msgf("[AppContext.TagNamesToIDs] Tag not found: %s", name)
	}
	return ids
}

func (r *AppContext) loadTags() error {
	if r.tags == nil {
		if err := r.TenantDatabase().Where("active").Find(&r.tags).Error; err != nil {
			r.Logger().Error().Err(err).Msg("[AppContext.TagNamesToIDs] Failed to get tags")
			return err
		}
	}
	return nil
}

func (r *AppContext) TenantDatabaseUnscoped() *gorm.DB {
	ctx := logging.InjectLoggerInContext(context.Background())
	return r.tenantDB.WithContext(ctx)
}

func (r *AppContext) TenancyDB() *gorm.DB {
	return r.mta.TenancyDB().WithContext(r.Request().Context())
}
func (r *AppContext) TenancyDatabaseUnscoped() *gorm.DB {
	ctx := logging.InjectLoggerInContext(context.Background())
	return r.mta.TenancyDB().WithContext(ctx)
}

func (r *AppContext) CurrentSiteViewModel() tenancyModels.SiteViewModel {
	if r.currentSiteVM != nil {
		return *r.currentSiteVM
	}
	currentSite := r.CurrentSite()
	var ss tenancyModels.SiteSettings
	_ = json.Unmarshal(currentSite.Settings, &ss)

	r.currentSiteVM = &tenancyModels.SiteViewModel{
		Site: tenancyModels.Site{
			BaseSite: tenancyModels.BaseSite{
				ID:            currentSite.ID,
				TenantID:      r.tenantID,
				Name:          currentSite.Name,
				PrimaryDomain: currentSite.PrimaryDomain,
				Description:   currentSite.Description,
				Type:          tenancyModels.SiteType(currentSite.Type),
				Active:        true,
			},
			Created:  currentSite.Created,
			Settings: currentSite.Settings,
			Tags:     currentSite.Tags,
		},
		SiteSettings: ss,
	}

	return *r.currentSiteVM
}

func (r *AppContext) CurrentSite() multitenancy.Site {
	if r.CurrentSiteID() == uuid.Nil {
		return multitenancy.Site{}
	}

	if r.currentSite != nil {
		return *r.currentSite
	}

	if site, err := r.mta.SiteByID(r.tenantID, r.CurrentSiteID()); err == nil {
		r.currentSite = &site
		return site
	}

	return multitenancy.Site{}
}

func (r *AppContext) CurrentSiteID() uuid.UUID {
	if r.currentSiteID != nil {
		return *r.currentSiteID
	}

	if len(r.Maps()["siteId"]) > 0 {
		siteId, err := uuid.FromString(r.Maps()["siteId"][0])
		if err == nil {
			if site, errSite := r.mta.SiteByID(r.tenantID, siteId); errSite == nil {
				r.currentSite = &site
				r.currentSiteID = &siteId
				return siteId
			} else {
				r.Logger().Error().Err(errSite).Msgf("Site not found in tenant: site: %s, tenant: %s", siteId, r.tenantID)
			}
		}
	}

	// TODO: find a better way to deal with it
	return uuid.Nil
}

func (r *AppContext) PublicAccount() identity.PublicAccount {
	return r.accountAccessor.PublicAccount()
}

func (r *AppContext) MultitenancyAccessor() multitenancy.Accessor {
	return r.mta
}

func (r *AppContext) Account() *identity.Account {
	acc := r.accountAccessor.Account()
	return &acc
}

func (r *AppContext) Request() *http.Request {
	return r.request
}

func (r *AppContext) TenantID() uuid.UUID {
	return r.tenantID
}

func (r *AppContext) Tenant() multitenancy.Tenant {
	if r.tenant != nil {
		return *r.tenant
	}

	if tenant, err := r.mta.TenantByID(r.tenantID); err == nil {
		r.tenant = &tenant
		return tenant
	} else {
		r.Logger().Error().Err(err).Msgf("[AppContext.Tenant] Failed to get tenant by id: %s", r.tenantID)
	}

	return multitenancy.Tenant{}
}

func (r *AppContext) Timezone() string {
	if r.tenant == nil {
		if tenant, err := r.mta.TenantByID(r.tenantID); err != nil {
			r.Logger().Error().Err(err).Msgf("[AppContext.Timezone] Failed to get tenant by id: %s", r.tenantID)
			return "America/Montreal"
		} else {
			r.tenant = &tenant
		}
	}

	var tz string
	if err := jsonxx.UnmarshalJSONAtPath(r.tenant.Settings, "timezone", &tz); err != nil {
		r.Logger().Error().Err(err).Msgf("[AppContext.Timezone] Failed to get timezone from tenant settings: %s", r.tenant.Settings)
		return "America/Montreal"
	}
	return tz
}

func (r *AppContext) TimezoneLocation() *time.Location {
	loc, err := time.LoadLocation(r.Timezone())
	if err != nil {
		return time.UTC
	}
	return loc
}

func (r *AppContext) GoogleAdminByEmail(email string) (string, error) {
	emailDomain := email[strings.Index(email, "@")+1:]
	return r.mta.GoogleAdminByEmailDomain(emailDomain)
}

// CurrentSiteIDNullable deprecated
func (r *AppContext) CurrentSiteIDNullable() uuid.NullUUID {
	if r.CurrentSiteID() == uuid.Nil {
		return uuid.NullUUID{}
	}
	return uuid.NullUUID{UUID: r.CurrentSiteID(), Valid: true}
}

func (r *AppContext) PathAsFolders(path string) string {
	if len(path) == 0 {
		return "/"
	}

	if r.folders == nil {
		type folder struct {
			ID       uuid.UUID
			Filename string
		}
		var folders []folder
		if err := r.TenantDatabase().Table("document").Where("active AND type = 'folder'").Find(&folders).Error; err != nil {
			r.Logger().Error().Err(err).Msg("[AppContext.Path] Failed to get folders")
			return path
		}
		r.folders = map[string]string{}
		for _, f := range folders {
			r.folders[strings.ReplaceAll(f.ID.String(), "-", "")] = f.Filename
		}
	}

	parts := strings.Split(path, ".")
	for i, part := range parts {
		if name, ok := r.folders[part]; ok {
			parts[i] = name
		}
	}
	return "/" + strings.Join(parts, "/")
}

func (r *AppContext) Workspace() string {
	workspace := r.Request().Form.Get("ie_workspace")
	if len(workspace) > 0 {
		return workspace
	}

	workspace = r.Request().PathValue("workspace")
	if len(workspace) > 0 {
		return workspace
	}

	return "live"
}

func (r *AppContext) WorkspacesInTenant() []string {
	if r.workspaces != nil {
		return r.workspaces
	}

	var workspaces []string
	if err := r.TenantDatabase().Table("workspaces").Select("id").Where("active").Pluck("id", &workspaces).Error; err != nil {
		r.Logger().Error().Err(err).Msg("[AppContext.Workspaces] Failed to get workspaces")
		return []string{"live", "draft"}
	}
	r.workspaces = workspaces
	return workspaces
}

// EditingSession returns the editing session key from the request. It fails if the key is present but is not a valid timestamp.
func (r *AppContext) EditingSession() (*time.Time, error) {
	if len(r.Request().Form.Get("ie_editing_session")) > 0 {
		if t, err := dateparse.ParseAny(r.Request().Form.Get("ie_editing_session")); err == nil {
			return &t, nil
		} else {
			return nil, err
		}
	}

	return nil, nil
}
